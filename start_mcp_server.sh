#!/bin/bash

# Intraday Alpha Assistant MCP Server Launcher
# This script ensures the server starts with the correct Python environment

# Set the project directory
PROJECT_DIR="/Users/<USER>/Documents/augment-projects/option-buy-mcp"

# Set Python path
export PYTHONPATH="$PROJECT_DIR"

# Change to project directory
cd "$PROJECT_DIR"

# Use the specific Python version
exec /Users/<USER>/.pyenv/versions/3.10.13/bin/python3 src/server.py
