# 🔥 Intraday Alpha Assistant - MCP for <PERSON>

A personalized Claude Des<PERSON>op MCP (Model Context Protocol) server that provides real-time Nifty Option Buying decisions through comprehensive technical analysis.

## ✅ Core Objective

Help you decide:
- Whether to buy Nifty CE/PE today (or stay out)
- Which strike price is favorable  
- What are ideal entry & exit levels
- Highlight top 3 strongest/weakest stocks

## 🚀 Features

### 📊 Real-time Market Analysis
- Live Nifty 50 index data and options chain
- Technical indicators (RSI, MACD, Bollinger Bands, Moving Averages)
- Support/Resistance level detection
- Volume and momentum analysis

### 🎯 Option-Specific Tools
- **Strike Recommendation**: Get optimal CE/PE strikes based on risk level
- **Greeks Analysis**: Delta, Gamma, Theta, Vega calculations
- **Entry/Exit Levels**: Precise position sizing and target calculations
- **Risk-Reward Analysis**: Comprehensive risk assessment

### 📈 Stock Screening
- Top 3 strongest stocks (momentum + volume)
- Top 3 weakest stocks (for put strategies)
- Sector-wise analysis
- Real-time strength scoring

### 🧠 Decision Engine
- AI-powered recommendation system
- Market sentiment analysis
- Trend strength assessment
- Risk management guidelines

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- <PERSON> app
- Internet connection for real-time data

### Step 1: <PERSON><PERSON> and <PERSON>up
```bash
git clone <your-repo-url>
cd option-buy-mcp
pip install -r requirements.txt
```

### Step 2: Configure Claude Desktop

Add this to your Claude Desktop MCP configuration file:

**On macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**On Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "intraday-alpha-assistant": {
      "command": "python",
      "args": ["/path/to/your/option-buy-mcp/src/server.py"],
      "env": {}
    }
  }
}
```

### Step 3: Start the MCP Server
```bash
python src/server.py
```

### Step 4: Restart Claude Desktop
Close and reopen Claude Desktop to load the new MCP server.

## 🎮 Usage

Once configured, you can use these commands in Claude Desktop:

### 📊 Market Analysis
```
Analyze Nifty options for today - should I buy CE or PE?
```

### 🎯 Strike Selection
```
Recommend the best strike price for Nifty CE with moderate risk
```

### 💰 Position Sizing
```
Calculate entry and exit levels for 19500 CE with ₹10,000 investment
```

### 📈 Stock Screening
```
Show me the top 3 strongest stocks today
```

### 🌡️ Market Sentiment
```
What's the current market sentiment on 15-minute timeframe?
```

## 🔧 Available Tools

| Tool | Description | Usage |
|------|-------------|-------|
| `analyze_nifty_options` | Complete CE/PE analysis with recommendations | Market direction and option type selection |
| `get_strike_recommendation` | Optimal strike selection based on risk level | Strike price optimization |
| `get_entry_exit_levels` | Position sizing and target calculations | Risk management and profit targets |
| `screen_top_stocks` | Identify strongest/weakest performers | Stock selection for directional trades |
| `get_market_sentiment` | Overall market trend and sentiment analysis | Market timing and bias confirmation |

## 📋 Example Interactions

### Morning Market Analysis
**You**: "Good morning! Should I trade Nifty options today?"

**Claude with Alpha Assistant**: 
```
🚀 INTRADAY ALPHA ASSISTANT - 26 Jun 2024, 09:30

📊 MARKET SNAPSHOT
• Nifty: 19,485 (+0.45%)
• Status: OPEN
• Trend: BULLISH (Strength: 0.7)
• Market Strength: BULLISH

🟢 BULLISH SIGNAL DETECTED

RECOMMENDED ACTION: BUY CALL OPTIONS (CE)
• Best Strike: 19500 CE (Score: 8.2/10)
• Strategy: Momentum-based call buying
• Entry: On pullback to support or breakout confirmation
• Target: Next resistance level
• Stop Loss: 40% of premium paid
```

### Strike Selection
**You**: "What's the best strike for aggressive CE buying?"

**Claude**: Provides detailed strike analysis with Greeks, liquidity, and risk-reward ratios.

### Risk Management
**You**: "Calculate position size for 19500 CE with ₹15,000"

**Claude**: Returns precise lot calculations, stop-loss levels, and profit targets.

## ⚠️ Risk Disclaimer

**IMPORTANT**: This tool is for educational and informational purposes only.

- **Not Financial Advice**: All recommendations are based on technical analysis and should not be considered as financial advice
- **Risk Management**: Always use proper position sizing and stop-losses
- **Market Risk**: Options trading involves substantial risk and may result in total loss
- **Do Your Research**: Combine this tool's insights with your own analysis
- **Paper Trade First**: Test strategies with paper trading before using real money

## 🔧 Configuration

### Data Sources
- **Primary**: Yahoo Finance (yfinance)
- **Backup**: NSE India (when available)
- **Update Frequency**: Real-time during market hours

### Customization
You can modify the following in the source code:
- Risk levels and thresholds
- Technical indicator parameters
- Stock universe for screening
- Recommendation logic

## 🐛 Troubleshooting

### Common Issues

1. **MCP Server Not Loading**
   - Check Python path in configuration
   - Ensure all dependencies are installed
   - Verify file permissions

2. **No Data Available**
   - Check internet connection
   - Verify market hours (9:15 AM - 3:30 PM IST)
   - Try restarting the server

3. **Slow Performance**
   - Reduce stock screening universe
   - Increase API call delays
   - Check system resources

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export LOG_LEVEL=DEBUG
python src/server.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Claude Desktop**: For the amazing MCP framework
- **Yahoo Finance**: For reliable market data
- **TA-Lib**: For technical analysis indicators
- **Trading Community**: For insights and feedback

## 📞 Support

For issues, questions, or suggestions:
- Create an issue on GitHub
- Join our Discord community
- Email: <EMAIL>

---

**Happy Trading! 🚀📈**

*Remember: The best trade is sometimes no trade. Always prioritize capital preservation over profits.*
