"""
Option Analyzer Module
Specialized analysis for Nifty options trading
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import math

logger = logging.getLogger(__name__)

class OptionAnalyzer:
    def __init__(self):
        self.risk_free_rate = 0.06  # 6% risk-free rate
        self.volatility_cache = {}
    
    async def analyze_options(self, options_data: Dict[str, Any], 
                            technical_signals: Dict[str, Any], 
                            option_type: str = "both") -> Dict[str, Any]:
        """Comprehensive options analysis"""
        try:
            if 'options' not in options_data:
                return {"error": "No options data available"}
            
            underlying_price = options_data['underlying_price']
            options_chain = options_data['options']
            
            analysis = {
                "underlying_price": underlying_price,
                "expiry_date": options_data['expiry_date'],
                "analysis_timestamp": datetime.now().isoformat(),
                "option_recommendations": {},
                "greeks_analysis": {},
                "volatility_analysis": {},
                "liquidity_analysis": {},
                "risk_reward_analysis": {}
            }
            
            if option_type in ["CE", "both"]:
                ce_analysis = await self._analyze_call_options(
                    options_chain, underlying_price, technical_signals
                )
                analysis["option_recommendations"]["CE"] = ce_analysis
            
            if option_type in ["PE", "both"]:
                pe_analysis = await self._analyze_put_options(
                    options_chain, underlying_price, technical_signals
                )
                analysis["option_recommendations"]["PE"] = pe_analysis
            
            # Overall Greeks analysis
            analysis["greeks_analysis"] = self._calculate_portfolio_greeks(options_chain, underlying_price)
            
            # Volatility smile analysis
            analysis["volatility_analysis"] = self._analyze_volatility_smile(options_chain)
            
            # Liquidity analysis
            analysis["liquidity_analysis"] = self._analyze_liquidity(options_chain)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in options analysis: {e}")
            return {"error": str(e)}
    
    async def _analyze_call_options(self, options_chain: List[Dict], 
                                  underlying_price: float, 
                                  technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze call options specifically"""
        try:
            ce_recommendations = []
            
            for option in options_chain:
                strike = option['strike']
                ce_price = option['ce_price']
                ce_volume = option['ce_volume']
                ce_oi = option['ce_oi']
                ce_iv = option['ce_iv']
                
                # Calculate moneyness
                moneyness = strike / underlying_price
                
                # Skip if too far OTM or ITM
                if moneyness > 1.05 or moneyness < 0.95:
                    continue
                
                # Calculate Greeks (simplified)
                greeks = self._calculate_option_greeks(
                    underlying_price, strike, ce_price, "CE", ce_iv
                )
                
                # Score based on technical analysis
                technical_score = self._score_option_with_technicals(
                    strike, underlying_price, technical_signals, "CE"
                )
                
                # Liquidity score
                liquidity_score = min(10, (ce_volume + ce_oi) / 1000)
                
                # Risk-reward calculation
                risk_reward = self._calculate_risk_reward(
                    ce_price, strike, underlying_price, "CE", technical_signals
                )
                
                # Overall score
                overall_score = (technical_score * 0.4 + 
                               liquidity_score * 0.3 + 
                               risk_reward['score'] * 0.3)
                
                ce_recommendations.append({
                    "strike": strike,
                    "price": ce_price,
                    "moneyness": round(moneyness, 3),
                    "volume": ce_volume,
                    "open_interest": ce_oi,
                    "implied_volatility": ce_iv,
                    "greeks": greeks,
                    "technical_score": round(technical_score, 2),
                    "liquidity_score": round(liquidity_score, 2),
                    "risk_reward": risk_reward,
                    "overall_score": round(overall_score, 2),
                    "recommendation": self._get_recommendation_text(overall_score)
                })
            
            # Sort by overall score
            ce_recommendations.sort(key=lambda x: x['overall_score'], reverse=True)
            
            return {
                "top_recommendations": ce_recommendations[:3],
                "all_analyzed": ce_recommendations,
                "summary": self._generate_ce_summary(ce_recommendations, technical_signals)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing call options: {e}")
            return {"error": str(e)}
    
    async def _analyze_put_options(self, options_chain: List[Dict], 
                                 underlying_price: float, 
                                 technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze put options specifically"""
        try:
            pe_recommendations = []
            
            for option in options_chain:
                strike = option['strike']
                pe_price = option['pe_price']
                pe_volume = option['pe_volume']
                pe_oi = option['pe_oi']
                pe_iv = option['pe_iv']
                
                # Calculate moneyness
                moneyness = strike / underlying_price
                
                # Skip if too far OTM or ITM
                if moneyness > 1.05 or moneyness < 0.95:
                    continue
                
                # Calculate Greeks (simplified)
                greeks = self._calculate_option_greeks(
                    underlying_price, strike, pe_price, "PE", pe_iv
                )
                
                # Score based on technical analysis
                technical_score = self._score_option_with_technicals(
                    strike, underlying_price, technical_signals, "PE"
                )
                
                # Liquidity score
                liquidity_score = min(10, (pe_volume + pe_oi) / 1000)
                
                # Risk-reward calculation
                risk_reward = self._calculate_risk_reward(
                    pe_price, strike, underlying_price, "PE", technical_signals
                )
                
                # Overall score
                overall_score = (technical_score * 0.4 + 
                               liquidity_score * 0.3 + 
                               risk_reward['score'] * 0.3)
                
                pe_recommendations.append({
                    "strike": strike,
                    "price": pe_price,
                    "moneyness": round(moneyness, 3),
                    "volume": pe_volume,
                    "open_interest": pe_oi,
                    "implied_volatility": pe_iv,
                    "greeks": greeks,
                    "technical_score": round(technical_score, 2),
                    "liquidity_score": round(liquidity_score, 2),
                    "risk_reward": risk_reward,
                    "overall_score": round(overall_score, 2),
                    "recommendation": self._get_recommendation_text(overall_score)
                })
            
            # Sort by overall score
            pe_recommendations.sort(key=lambda x: x['overall_score'], reverse=True)
            
            return {
                "top_recommendations": pe_recommendations[:3],
                "all_analyzed": pe_recommendations,
                "summary": self._generate_pe_summary(pe_recommendations, technical_signals)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing put options: {e}")
            return {"error": str(e)}
    
    def _calculate_option_greeks(self, spot: float, strike: float, 
                               option_price: float, option_type: str, 
                               iv: float) -> Dict[str, float]:
        """Calculate option Greeks (simplified Black-Scholes)"""
        try:
            # Time to expiry (assuming 1 week for simplicity)
            time_to_expiry = 7 / 365.0
            
            # Simplified Greeks calculation
            d1 = (math.log(spot / strike) + (self.risk_free_rate + 0.5 * (iv/100)**2) * time_to_expiry) / ((iv/100) * math.sqrt(time_to_expiry))
            d2 = d1 - (iv/100) * math.sqrt(time_to_expiry)
            
            # Standard normal CDF approximation
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if option_type == "CE":
                delta = norm_cdf(d1)
                theta = -(spot * norm_cdf(d1) * (iv/100) / (2 * math.sqrt(time_to_expiry)) + 
                         self.risk_free_rate * strike * math.exp(-self.risk_free_rate * time_to_expiry) * norm_cdf(d2)) / 365
            else:  # PE
                delta = norm_cdf(d1) - 1
                theta = -(spot * norm_cdf(d1) * (iv/100) / (2 * math.sqrt(time_to_expiry)) - 
                         self.risk_free_rate * strike * math.exp(-self.risk_free_rate * time_to_expiry) * norm_cdf(-d2)) / 365
            
            # Gamma (same for calls and puts)
            gamma = math.exp(-d1**2 / 2) / (spot * (iv/100) * math.sqrt(2 * math.pi * time_to_expiry))
            
            # Vega (same for calls and puts)
            vega = spot * math.sqrt(time_to_expiry) * math.exp(-d1**2 / 2) / math.sqrt(2 * math.pi) / 100
            
            return {
                "delta": round(delta, 3),
                "gamma": round(gamma, 4),
                "theta": round(theta, 2),
                "vega": round(vega, 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating Greeks: {e}")
            return {"delta": 0, "gamma": 0, "theta": 0, "vega": 0}
    
    def _score_option_with_technicals(self, strike: float, spot: float, 
                                    technical_signals: Dict[str, Any], 
                                    option_type: str) -> float:
        """Score option based on technical analysis"""
        try:
            score = 5.0  # Base score
            
            # Get trend analysis
            trend_analysis = technical_signals.get('trend_analysis', {})
            trend_direction = trend_analysis.get('direction', 'sideways')
            trend_strength = trend_analysis.get('strength', 0.5)
            
            # Get support/resistance levels
            support_resistance = technical_signals.get('support_resistance', {})
            nearest_support = support_resistance.get('nearest_support')
            nearest_resistance = support_resistance.get('nearest_resistance')
            
            # Adjust score based on trend and option type
            if option_type == "CE":
                if trend_direction in ['bullish', 'strong_bullish']:
                    score += trend_strength * 3
                elif trend_direction in ['bearish', 'strong_bearish']:
                    score -= trend_strength * 2
                
                # Bonus if strike is near resistance (breakout potential)
                if nearest_resistance and abs(strike - nearest_resistance) / spot < 0.02:
                    score += 1
                    
            else:  # PE
                if trend_direction in ['bearish', 'strong_bearish']:
                    score += trend_strength * 3
                elif trend_direction in ['bullish', 'strong_bullish']:
                    score -= trend_strength * 2
                
                # Bonus if strike is near support (breakdown potential)
                if nearest_support and abs(strike - nearest_support) / spot < 0.02:
                    score += 1
            
            # Momentum indicators
            momentum = technical_signals.get('momentum_indicators', {})
            rsi = momentum.get('rsi')
            
            if rsi:
                if option_type == "CE" and rsi < 40:  # Oversold, good for calls
                    score += 1
                elif option_type == "PE" and rsi > 60:  # Overbought, good for puts
                    score += 1
            
            return max(0, min(10, score))
            
        except Exception as e:
            logger.error(f"Error scoring option with technicals: {e}")
            return 5.0
    
    def _calculate_risk_reward(self, option_price: float, strike: float, 
                             spot: float, option_type: str, 
                             technical_signals: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate risk-reward metrics"""
        try:
            # Maximum risk is the premium paid
            max_risk = option_price
            
            # Estimate potential reward based on technical levels
            support_resistance = technical_signals.get('support_resistance', {})
            
            if option_type == "CE":
                # For calls, reward is based on upside potential
                nearest_resistance = support_resistance.get('nearest_resistance', spot * 1.02)
                potential_reward = max(0, nearest_resistance - strike - option_price)
            else:
                # For puts, reward is based on downside potential
                nearest_support = support_resistance.get('nearest_support', spot * 0.98)
                potential_reward = max(0, strike - nearest_support - option_price)
            
            # Risk-reward ratio
            risk_reward_ratio = potential_reward / max_risk if max_risk > 0 else 0
            
            # Score based on risk-reward ratio
            if risk_reward_ratio >= 2:
                score = 10
            elif risk_reward_ratio >= 1.5:
                score = 8
            elif risk_reward_ratio >= 1:
                score = 6
            elif risk_reward_ratio >= 0.5:
                score = 4
            else:
                score = 2
            
            return {
                "max_risk": round(max_risk, 2),
                "potential_reward": round(potential_reward, 2),
                "risk_reward_ratio": round(risk_reward_ratio, 2),
                "score": score
            }
            
        except Exception as e:
            logger.error(f"Error calculating risk-reward: {e}")
            return {"max_risk": option_price, "potential_reward": 0, "risk_reward_ratio": 0, "score": 1}
    
    def _get_recommendation_text(self, score: float) -> str:
        """Convert numerical score to recommendation text"""
        if score >= 8:
            return "STRONG BUY"
        elif score >= 6.5:
            return "BUY"
        elif score >= 5:
            return "HOLD/NEUTRAL"
        elif score >= 3:
            return "AVOID"
        else:
            return "STRONG AVOID"
    
    def _generate_ce_summary(self, recommendations: List[Dict], 
                           technical_signals: Dict[str, Any]) -> str:
        """Generate summary for call options"""
        if not recommendations:
            return "No suitable call options found"
        
        best_option = recommendations[0]
        trend = technical_signals.get('trend_analysis', {}).get('direction', 'sideways')
        
        return f"""
**CALL OPTIONS SUMMARY**
• Best Strike: {best_option['strike']} (Score: {best_option['overall_score']})
• Current Trend: {trend.upper()}
• Top 3 strikes analyzed with liquidity and technical factors
• Risk-Reward ratio considered for each recommendation
        """.strip()
    
    def _generate_pe_summary(self, recommendations: List[Dict], 
                           technical_signals: Dict[str, Any]) -> str:
        """Generate summary for put options"""
        if not recommendations:
            return "No suitable put options found"
        
        best_option = recommendations[0]
        trend = technical_signals.get('trend_analysis', {}).get('direction', 'sideways')
        
        return f"""
**PUT OPTIONS SUMMARY**
• Best Strike: {best_option['strike']} (Score: {best_option['overall_score']})
• Current Trend: {trend.upper()}
• Top 3 strikes analyzed with liquidity and technical factors
• Risk-Reward ratio considered for each recommendation
        """.strip()
    
    def _calculate_portfolio_greeks(self, options_chain: List[Dict], 
                                  underlying_price: float) -> Dict[str, Any]:
        """Calculate overall portfolio Greeks"""
        # Simplified portfolio Greeks calculation
        return {
            "net_delta": "Calculated based on position sizes",
            "net_gamma": "Risk of delta changes",
            "net_theta": "Time decay impact",
            "net_vega": "Volatility sensitivity"
        }
    
    def _analyze_volatility_smile(self, options_chain: List[Dict]) -> Dict[str, Any]:
        """Analyze implied volatility smile"""
        ce_ivs = [opt['ce_iv'] for opt in options_chain]
        pe_ivs = [opt['pe_iv'] for opt in options_chain]
        
        return {
            "ce_iv_range": f"{min(ce_ivs):.1f}% - {max(ce_ivs):.1f}%",
            "pe_iv_range": f"{min(pe_ivs):.1f}% - {max(pe_ivs):.1f}%",
            "avg_ce_iv": round(sum(ce_ivs) / len(ce_ivs), 1),
            "avg_pe_iv": round(sum(pe_ivs) / len(pe_ivs), 1)
        }
    
    def _analyze_liquidity(self, options_chain: List[Dict]) -> Dict[str, Any]:
        """Analyze options liquidity"""
        total_ce_volume = sum(opt['ce_volume'] for opt in options_chain)
        total_pe_volume = sum(opt['pe_volume'] for opt in options_chain)
        total_ce_oi = sum(opt['ce_oi'] for opt in options_chain)
        total_pe_oi = sum(opt['pe_oi'] for opt in options_chain)
        
        return {
            "total_ce_volume": total_ce_volume,
            "total_pe_volume": total_pe_volume,
            "total_ce_oi": total_ce_oi,
            "total_pe_oi": total_pe_oi,
            "pcr_volume": round(total_pe_volume / total_ce_volume, 2) if total_ce_volume > 0 else 0,
            "pcr_oi": round(total_pe_oi / total_ce_oi, 2) if total_ce_oi > 0 else 0
        }
    
    async def recommend_strike(self, option_type: str, risk_level: str = "moderate") -> str:
        """Recommend optimal strike based on risk level"""
        try:
            # This would use current market data and analysis
            # For now, return a formatted recommendation
            
            risk_multipliers = {
                "conservative": 0.98 if option_type == "PE" else 1.02,
                "moderate": 1.00,
                "aggressive": 1.02 if option_type == "PE" else 0.98
            }
            
            multiplier = risk_multipliers.get(risk_level, 1.00)
            
            return f"""
🎯 **STRIKE RECOMMENDATION** ({option_type})

**Risk Level**: {risk_level.upper()}
**Recommended Strategy**: 
• Look for strikes near {multiplier:.2f}x current Nifty level
• Focus on high volume/OI strikes
• Consider time decay (theta) impact
• Monitor implied volatility levels

**Entry Criteria**:
• Wait for technical confirmation
• Check support/resistance levels
• Ensure adequate liquidity
• Set stop-loss at 30-50% of premium
            """.strip()
            
        except Exception as e:
            logger.error(f"Error in strike recommendation: {e}")
            return f"Error generating strike recommendation: {str(e)}"
