"""
Decision Engine Module
Main recommendation logic for option trading decisions
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class DecisionEngine:
    def __init__(self):
        self.confidence_threshold = 0.6
        self.risk_levels = {
            "conservative": {"max_risk_percent": 2, "min_confidence": 0.8},
            "moderate": {"max_risk_percent": 5, "min_confidence": 0.6},
            "aggressive": {"max_risk_percent": 10, "min_confidence": 0.4}
        }
    
    async def generate_recommendation(self, nifty_data: Dict[str, Any], 
                                    technical_signals: Dict[str, Any], 
                                    option_analysis: Dict[str, Any]) -> str:
        """Generate comprehensive trading recommendation"""
        try:
            current_price = nifty_data['current_price']
            market_status = nifty_data.get('market_status', 'unknown')
            change_percent = nifty_data.get('change_percent', 0)
            
            # Analyze market conditions
            market_condition = self._assess_market_condition(nifty_data, technical_signals)
            
            # Get trend analysis
            trend_analysis = technical_signals.get('trend_analysis', {})
            trend_direction = trend_analysis.get('direction', 'sideways')
            trend_strength = trend_analysis.get('strength', 0.5)
            
            # Get momentum signals
            momentum = technical_signals.get('momentum_indicators', {})
            market_strength = technical_signals.get('market_strength', {})
            
            # Generate main recommendation
            recommendation = self._generate_main_recommendation(
                trend_direction, trend_strength, market_condition, option_analysis
            )
            
            # Format the complete recommendation
            formatted_recommendation = f"""
🚀 **INTRADAY ALPHA ASSISTANT** - {datetime.now().strftime('%d %b %Y, %H:%M')}

📊 **MARKET SNAPSHOT**
• Nifty: {current_price} ({change_percent:+.2f}%)
• Status: {market_status.upper()}
• Trend: {trend_direction.upper()} (Strength: {trend_strength:.1f})
• Market Strength: {market_strength.get('label', 'N/A').upper()}

{recommendation}

⚠️ **RISK MANAGEMENT**
• Never risk more than 2-5% of capital per trade
• Set stop-loss at 30-50% of premium paid
• Book profits at 50-100% gains
• Avoid trading in last 30 minutes

📈 **TECHNICAL LEVELS**
{self._format_technical_levels(technical_signals)}

🕐 **NEXT UPDATE**: Monitor for breakouts and volume spikes
            """.strip()
            
            return formatted_recommendation
            
        except Exception as e:
            logger.error(f"Error generating recommendation: {e}")
            return f"❌ Error generating recommendation: {str(e)}"
    
    def _assess_market_condition(self, nifty_data: Dict[str, Any], 
                               technical_signals: Dict[str, Any]) -> str:
        """Assess overall market condition"""
        try:
            change_percent = abs(nifty_data.get('change_percent', 0))
            volume_analysis = technical_signals.get('volume_analysis', {})
            volatility = technical_signals.get('volatility_indicators', {})
            
            # High volatility condition
            if change_percent > 1.5:
                return "high_volatility"
            
            # Check volume
            volume_signals = volume_analysis.get('signals', [])
            if 'high_volume' in volume_signals:
                return "high_activity"
            elif 'low_volume' in volume_signals:
                return "low_activity"
            
            # Check volatility indicators
            volatility_signals = volatility.get('signals', [])
            if any('bb_' in signal for signal in volatility_signals):
                return "range_bound"
            
            return "normal"
            
        except Exception as e:
            logger.error(f"Error assessing market condition: {e}")
            return "unknown"
    
    def _generate_main_recommendation(self, trend_direction: str, trend_strength: float,
                                    market_condition: str, option_analysis: Dict[str, Any]) -> str:
        """Generate the main trading recommendation"""
        try:
            recommendations = []
            
            # Primary recommendation based on trend
            if trend_direction in ['bullish', 'strong_bullish'] and trend_strength > 0.6:
                recommendations.append(self._generate_bullish_recommendation(option_analysis))
            elif trend_direction in ['bearish', 'strong_bearish'] and trend_strength > 0.6:
                recommendations.append(self._generate_bearish_recommendation(option_analysis))
            else:
                recommendations.append(self._generate_neutral_recommendation(option_analysis))
            
            # Market condition specific advice
            if market_condition == "high_volatility":
                recommendations.append("⚡ **HIGH VOLATILITY DETECTED** - Consider shorter timeframes and tighter stops")
            elif market_condition == "low_activity":
                recommendations.append("😴 **LOW ACTIVITY** - Wait for volume confirmation before entry")
            elif market_condition == "range_bound":
                recommendations.append("📦 **RANGE-BOUND MARKET** - Look for breakout/breakdown signals")
            
            return "\n\n".join(recommendations)
            
        except Exception as e:
            logger.error(f"Error generating main recommendation: {e}")
            return "❌ Unable to generate recommendation due to analysis error"
    
    def _generate_bullish_recommendation(self, option_analysis: Dict[str, Any]) -> str:
        """Generate bullish market recommendation"""
        try:
            ce_analysis = option_analysis.get('option_recommendations', {}).get('CE', {})
            top_ce = ce_analysis.get('top_recommendations', [])
            
            if top_ce:
                best_strike = top_ce[0]['strike']
                best_score = top_ce[0]['overall_score']
                
                return f"""
🟢 **BULLISH SIGNAL DETECTED**

**RECOMMENDED ACTION**: BUY CALL OPTIONS (CE)
• **Best Strike**: {best_strike} CE (Score: {best_score}/10)
• **Strategy**: Momentum-based call buying
• **Entry**: On pullback to support or breakout confirmation
• **Target**: Next resistance level
• **Stop Loss**: 40% of premium paid

**Alternative Strikes**:
{self._format_alternative_strikes(top_ce[1:3])}
                """.strip()
            else:
                return """
🟢 **BULLISH SIGNAL DETECTED**

**RECOMMENDED ACTION**: BUY CALL OPTIONS (CE)
• Look for ATM or slightly OTM calls
• Wait for volume confirmation
• Enter on dips for better risk-reward
                """.strip()
                
        except Exception as e:
            logger.error(f"Error generating bullish recommendation: {e}")
            return "🟢 **BULLISH SIGNAL** - Consider call options with proper risk management"
    
    def _generate_bearish_recommendation(self, option_analysis: Dict[str, Any]) -> str:
        """Generate bearish market recommendation"""
        try:
            pe_analysis = option_analysis.get('option_recommendations', {}).get('PE', {})
            top_pe = pe_analysis.get('top_recommendations', [])
            
            if top_pe:
                best_strike = top_pe[0]['strike']
                best_score = top_pe[0]['overall_score']
                
                return f"""
🔴 **BEARISH SIGNAL DETECTED**

**RECOMMENDED ACTION**: BUY PUT OPTIONS (PE)
• **Best Strike**: {best_strike} PE (Score: {best_score}/10)
• **Strategy**: Momentum-based put buying
• **Entry**: On bounce to resistance or breakdown confirmation
• **Target**: Next support level
• **Stop Loss**: 40% of premium paid

**Alternative Strikes**:
{self._format_alternative_strikes(top_pe[1:3])}
                """.strip()
            else:
                return """
🔴 **BEARISH SIGNAL DETECTED**

**RECOMMENDED ACTION**: BUY PUT OPTIONS (PE)
• Look for ATM or slightly OTM puts
• Wait for breakdown confirmation
• Enter on bounces for better risk-reward
                """.strip()
                
        except Exception as e:
            logger.error(f"Error generating bearish recommendation: {e}")
            return "🔴 **BEARISH SIGNAL** - Consider put options with proper risk management"
    
    def _generate_neutral_recommendation(self, option_analysis: Dict[str, Any]) -> str:
        """Generate neutral market recommendation"""
        return """
⚪ **NEUTRAL/SIDEWAYS MARKET**

**RECOMMENDED ACTION**: STAY OUT OR WAIT
• No clear directional bias detected
• Wait for breakout/breakdown signals
• Consider range-bound strategies if experienced
• Focus on risk management over profits

**What to Watch**:
• Volume spikes indicating direction
• Break of key support/resistance levels
• Technical indicator confirmations
        """.strip()
    
    def _format_alternative_strikes(self, alternatives: List[Dict]) -> str:
        """Format alternative strike recommendations"""
        if not alternatives:
            return "• No suitable alternatives found"
        
        formatted = []
        for i, alt in enumerate(alternatives, 2):
            formatted.append(f"• Option {i}: {alt['strike']} (Score: {alt['overall_score']}/10)")
        
        return "\n".join(formatted)
    
    def _format_technical_levels(self, technical_signals: Dict[str, Any]) -> str:
        """Format technical support/resistance levels"""
        try:
            support_resistance = technical_signals.get('support_resistance', {})
            trend_analysis = technical_signals.get('trend_analysis', {})
            
            levels = []
            
            # Support/Resistance
            if support_resistance.get('nearest_resistance'):
                levels.append(f"• Resistance: {support_resistance['nearest_resistance']}")
            if support_resistance.get('nearest_support'):
                levels.append(f"• Support: {support_resistance['nearest_support']}")
            
            # Moving averages
            if trend_analysis.get('sma_21'):
                levels.append(f"• SMA 21: {trend_analysis['sma_21']}")
            if trend_analysis.get('ema_21'):
                levels.append(f"• EMA 21: {trend_analysis['ema_21']}")
            
            # Bollinger Bands
            volatility = technical_signals.get('volatility_indicators', {})
            bb = volatility.get('bollinger_bands', {})
            if bb.get('upper') and bb.get('lower'):
                levels.append(f"• BB Upper: {bb['upper']}")
                levels.append(f"• BB Lower: {bb['lower']}")
            
            return "\n".join(levels) if levels else "• Technical levels being calculated..."
            
        except Exception as e:
            logger.error(f"Error formatting technical levels: {e}")
            return "• Error retrieving technical levels"
    
    async def calculate_entry_exit_levels(self, strike_price: float, 
                                        option_type: str, 
                                        investment_amount: float = 10000) -> str:
        """Calculate specific entry and exit levels"""
        try:
            # Estimate option premium (simplified)
            estimated_premium = strike_price * 0.02  # 2% of strike as rough estimate
            
            # Calculate position size
            lots_possible = int(investment_amount / (estimated_premium * 50))  # 50 is lot size
            actual_investment = lots_possible * estimated_premium * 50
            
            # Calculate levels
            stop_loss_level = estimated_premium * 0.6  # 40% loss
            target_1 = estimated_premium * 1.5  # 50% profit
            target_2 = estimated_premium * 2.0  # 100% profit
            
            return f"""
💰 **POSITION SIZING & LEVELS**

**Strike**: {strike_price} {option_type}
**Investment Amount**: ₹{investment_amount:,.0f}
**Estimated Premium**: ₹{estimated_premium:.1f}

**POSITION DETAILS**:
• Lots: {lots_possible}
• Actual Investment: ₹{actual_investment:,.0f}
• Quantity: {lots_possible * 50} shares

**ENTRY/EXIT LEVELS**:
• **Entry**: ₹{estimated_premium:.1f} (current estimate)
• **Stop Loss**: ₹{stop_loss_level:.1f} (-40%)
• **Target 1**: ₹{target_1:.1f} (+50%)
• **Target 2**: ₹{target_2:.1f} (+100%)

**RISK MANAGEMENT**:
• Maximum Loss: ₹{(estimated_premium - stop_loss_level) * lots_possible * 50:,.0f}
• Potential Profit (T1): ₹{(target_1 - estimated_premium) * lots_possible * 50:,.0f}
• Potential Profit (T2): ₹{(target_2 - estimated_premium) * lots_possible * 50:,.0f}

⚠️ **Note**: These are estimates. Actual option prices vary based on volatility, time decay, and market conditions.
            """.strip()
            
        except Exception as e:
            logger.error(f"Error calculating entry/exit levels: {e}")
            return f"❌ Error calculating levels: {str(e)}"
