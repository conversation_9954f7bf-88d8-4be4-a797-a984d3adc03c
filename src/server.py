#!/usr/bin/env python3
"""
Intraday Alpha Assistant MCP Server
Provides real-time Nifty options analysis and recommendations for Claude Desktop
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetcher import DataFetcher
from technical_analysis import TechnicalAnalyzer
from option_analyzer import OptionAnalyzer
from decision_engine import DecisionEngine
from stock_screener import StockScreener

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("intraday-alpha-assistant")

class IntradayAlphaServer:
    def __init__(self):
        self.server = Server("intraday-alpha-assistant")
        self.data_fetcher = DataFetcher()
        self.technical_analyzer = TechnicalAnalyzer()
        self.option_analyzer = OptionAnalyzer()
        self.decision_engine = DecisionEngine()
        self.stock_screener = StockScreener()

        # Setup tools and resources
        self._setup_tools()
        self._setup_resources()

    def _setup_tools(self):
        """Register MCP tools"""

        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            return [
                Tool(
                    name="analyze_nifty_options",
                    description="Analyze Nifty CE/PE options and provide buy/sell recommendations",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "option_type": {
                                "type": "string",
                                "enum": ["CE", "PE", "both"],
                                "description": "Type of option to analyze"
                            },
                            "expiry_date": {
                                "type": "string",
                                "description": "Option expiry date (YYYY-MM-DD), defaults to nearest Thursday"
                            }
                        }
                    }
                ),
                Tool(
                    name="get_strike_recommendation",
                    description="Get optimal strike price recommendations based on technical analysis",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "option_type": {
                                "type": "string",
                                "enum": ["CE", "PE"],
                                "description": "Type of option (Call or Put)"
                            },
                            "risk_level": {
                                "type": "string",
                                "enum": ["conservative", "moderate", "aggressive"],
                                "description": "Risk tolerance level"
                            }
                        },
                        "required": ["option_type"]
                    }
                ),
                Tool(
                    name="get_entry_exit_levels",
                    description="Calculate ideal entry and exit levels for option trades",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "strike_price": {
                                "type": "number",
                                "description": "Strike price of the option"
                            },
                            "option_type": {
                                "type": "string",
                                "enum": ["CE", "PE"],
                                "description": "Type of option"
                            },
                            "investment_amount": {
                                "type": "number",
                                "description": "Amount to invest in rupees"
                            }
                        },
                        "required": ["strike_price", "option_type"]
                    }
                ),
                Tool(
                    name="screen_top_stocks",
                    description="Identify top 3 strongest/weakest stocks for the day",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "screen_type": {
                                "type": "string",
                                "enum": ["strongest", "weakest", "both"],
                                "description": "Type of screening to perform"
                            },
                            "sector": {
                                "type": "string",
                                "description": "Specific sector to screen (optional)"
                            }
                        }
                    }
                ),
                Tool(
                    name="get_market_sentiment",
                    description="Analyze overall market sentiment and trend direction",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "timeframe": {
                                "type": "string",
                                "enum": ["1m", "5m", "15m", "1h", "1d"],
                                "description": "Timeframe for analysis"
                            }
                        }
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "analyze_nifty_options":
                    return await self._analyze_nifty_options(arguments)
                elif name == "get_strike_recommendation":
                    return await self._get_strike_recommendation(arguments)
                elif name == "get_entry_exit_levels":
                    return await self._get_entry_exit_levels(arguments)
                elif name == "screen_top_stocks":
                    return await self._screen_top_stocks(arguments)
                elif name == "get_market_sentiment":
                    return await self._get_market_sentiment(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    def _setup_resources(self):
        """Register MCP resources"""

        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            return [
                Resource(
                    uri="nifty://data/current",
                    name="Current Nifty Data",
                    description="Real-time Nifty index and options data",
                    mimeType="application/json"
                ),
                Resource(
                    uri="technical://indicators/current",
                    name="Technical Indicators",
                    description="Current technical analysis indicators and signals",
                    mimeType="application/json"
                )
            ]

        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Handle resource reads"""
            if uri == "nifty://data/current":
                data = await self.data_fetcher.get_nifty_data()
                return json.dumps(data, indent=2)
            elif uri == "technical://indicators/current":
                indicators = await self.technical_analyzer.get_current_indicators()
                return json.dumps(indicators, indent=2)
            else:
                raise ValueError(f"Unknown resource: {uri}")

    async def _analyze_nifty_options(self, args: Dict[str, Any]) -> List[TextContent]:
        """Analyze Nifty options and provide recommendations"""
        option_type = args.get("option_type", "both")
        expiry_date = args.get("expiry_date")

        # Get current market data
        nifty_data = await self.data_fetcher.get_nifty_data()
        options_data = await self.data_fetcher.get_options_chain(expiry_date)

        # Perform technical analysis
        technical_signals = await self.technical_analyzer.analyze_nifty(nifty_data)

        # Get option analysis
        option_analysis = await self.option_analyzer.analyze_options(
            options_data, technical_signals, option_type
        )

        # Generate decision
        recommendation = await self.decision_engine.generate_recommendation(
            nifty_data, technical_signals, option_analysis
        )

        return [TextContent(type="text", text=recommendation)]

    async def _get_strike_recommendation(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get optimal strike price recommendations"""
        option_type = args["option_type"]
        risk_level = args.get("risk_level", "moderate")

        # Implementation will be added in option_analyzer.py
        recommendation = await self.option_analyzer.recommend_strike(option_type, risk_level)

        return [TextContent(type="text", text=recommendation)]

    async def _get_entry_exit_levels(self, args: Dict[str, Any]) -> List[TextContent]:
        """Calculate entry and exit levels"""
        strike_price = args["strike_price"]
        option_type = args["option_type"]
        investment_amount = args.get("investment_amount", 10000)

        levels = await self.decision_engine.calculate_entry_exit_levels(
            strike_price, option_type, investment_amount
        )

        return [TextContent(type="text", text=levels)]

    async def _screen_top_stocks(self, args: Dict[str, Any]) -> List[TextContent]:
        """Screen top performing stocks"""
        screen_type = args.get("screen_type", "both")
        sector = args.get("sector")

        results = await self.stock_screener.screen_stocks(screen_type, sector)

        return [TextContent(type="text", text=results)]

    async def _get_market_sentiment(self, args: Dict[str, Any]) -> List[TextContent]:
        """Analyze market sentiment"""
        timeframe = args.get("timeframe", "15m")

        sentiment = await self.technical_analyzer.analyze_market_sentiment(timeframe)

        return [TextContent(type="text", text=sentiment)]

async def main():
    """Main entry point"""
    server_instance = IntradayAlphaServer()

    async with stdio_server() as (read_stream, write_stream):
        await server_instance.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="intraday-alpha-assistant",
                server_version="1.0.0",
                capabilities=server_instance.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
