#!/usr/bin/env python3
"""
Minimal working MCP server for Intraday Alpha Assistant
Based on official MCP Python SDK examples
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List

# Try different import approaches for MCP
try:
    from mcp.server.fastmcp import FastMCP
    USE_FASTMCP = True
except ImportError:
    try:
        from mcp.server import Server
        from mcp.server.stdio import stdio_server
        from mcp.server.models import InitializationOptions
        from mcp.types import Tool, TextContent, Resource
        USE_FASTMCP = False
    except ImportError as e:
        print(f"❌ MCP import failed: {e}")
        print("Please install MCP: pip install mcp")
        exit(1)

if USE_FASTMCP:
    # Using FastMCP approach
    mcp = FastMCP("Intraday Alpha Assistant")
    
    @mcp.tool()
    def test_connection() -> str:
        """Test MCP connection"""
        return "✅ MCP Connection successful! Intraday Alpha Assistant is working."
    
    @mcp.tool()
    def analyze_nifty_options(option_type: str = "both") -> str:
        """Analyze Nifty CE/PE options (simplified)"""
        return f"""
🚀 **INTRADAY ALPHA ASSISTANT** - {datetime.now().strftime('%d %b %Y, %H:%M')}

📊 **MARKET SNAPSHOT** (Demo Mode)
• Nifty: 19,500 (+0.5%)
• Status: OPEN
• Trend: BULLISH
• Market Strength: MODERATE

🎯 **RECOMMENDATION** ({option_type})
• Demo analysis for {option_type} options
• Real version includes live data
• Technical indicators analysis
• Risk management guidelines

✅ **MCP SERVER IS WORKING!**
        """.strip()
    
    @mcp.tool()
    def get_market_sentiment() -> str:
        """Get current market sentiment"""
        return """
🌡️ **MARKET SENTIMENT ANALYSIS**

**Overall Sentiment**: Cautiously Optimistic
**Trend Direction**: Sideways with Bullish Bias
**Volatility**: Moderate
**Volume**: Above Average

**Key Observations**:
• Market showing consolidation pattern
• Support holding at key levels
• Momentum indicators mixed

**Recommendation**: Wait for clear breakout signals
        """.strip()

else:
    # Using traditional MCP Server approach
    class MinimalIntradayServer:
        def __init__(self):
            self.server = Server("intraday-alpha-assistant")
            self._setup_tools()
        
        def _setup_tools(self):
            @self.server.list_tools()
            async def handle_list_tools() -> List[Tool]:
                return [
                    Tool(
                        name="test_connection",
                        description="Test MCP connection",
                        inputSchema={"type": "object", "properties": {}}
                    ),
                    Tool(
                        name="analyze_nifty_options",
                        description="Analyze Nifty options",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "option_type": {"type": "string", "enum": ["CE", "PE", "both"]}
                            }
                        }
                    )
                ]
            
            @self.server.call_tool()
            async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
                if name == "test_connection":
                    return [TextContent(type="text", text="✅ MCP Connection successful!")]
                elif name == "analyze_nifty_options":
                    option_type = arguments.get("option_type", "both")
                    analysis = f"""
🚀 **INTRADAY ALPHA ASSISTANT** - {datetime.now().strftime('%d %b %Y, %H:%M')}

📊 **MARKET SNAPSHOT** (Demo)
• Nifty: 19,500 (+0.5%)
• Trend: BULLISH
• Analysis for: {option_type}

✅ **MCP SERVER IS WORKING!**
                    """.strip()
                    return [TextContent(type="text", text=analysis)]
                else:
                    return [TextContent(type="text", text=f"Unknown tool: {name}")]

async def main():
    """Main entry point"""
    if USE_FASTMCP:
        print("Starting FastMCP server...")
        await mcp.run()
    else:
        print("Starting traditional MCP server...")
        server_instance = MinimalIntradayServer()
        
        async with stdio_server() as (read_stream, write_stream):
            await server_instance.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="intraday-alpha-assistant",
                    server_version="1.0.0-minimal"
                )
            )

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        print(f"Server error: {e}")
        import traceback
        traceback.print_exc()
