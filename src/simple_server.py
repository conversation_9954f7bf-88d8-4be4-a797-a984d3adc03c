#!/usr/bin/env python3
"""
Simplified Intraday Alpha Assistant MCP Server for testing
"""

import asyncio
import json
import logging
from typing import Any, Dict, List

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    LoggingLevel
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("intraday-alpha-simple")

class SimpleIntradayServer:
    def __init__(self):
        self.server = Server("intraday-alpha-assistant")
        self._setup_tools()
        self._setup_resources()
    
    def _setup_tools(self):
        """Register simplified MCP tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            return [
                Tool(
                    name="test_connection",
                    description="Test MCP connection",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "additionalProperties": False
                    }
                ),
                Tool(
                    name="analyze_nifty_options",
                    description="Analyze Nifty CE/PE options (simplified)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "option_type": {
                                "type": "string",
                                "enum": ["CE", "PE", "both"],
                                "description": "Type of option to analyze"
                            }
                        },
                        "additionalProperties": False
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "test_connection":
                    return [TextContent(
                        type="text", 
                        text="✅ MCP Connection successful! Intraday Alpha Assistant is working."
                    )]
                elif name == "analyze_nifty_options":
                    return await self._analyze_nifty_options_simple(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"❌ Error: {str(e)}")]
    
    def _setup_resources(self):
        """Register simplified MCP resources"""
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            return [
                Resource(
                    uri="test://status",
                    name="Server Status",
                    description="Current server status",
                    mimeType="application/json"
                )
            ]
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Handle resource reads"""
            if uri == "test://status":
                return json.dumps({
                    "status": "active",
                    "server": "intraday-alpha-assistant",
                    "version": "1.0.0-simple",
                    "timestamp": "2025-06-26T10:00:00Z"
                }, indent=2)
            else:
                raise ValueError(f"Unknown resource: {uri}")
    
    async def _analyze_nifty_options_simple(self, args: Dict[str, Any]) -> List[TextContent]:
        """Simplified Nifty options analysis"""
        option_type = args.get("option_type", "both")
        
        # Simulate analysis
        analysis = f"""
🚀 **INTRADAY ALPHA ASSISTANT** - Test Mode

📊 **MARKET SNAPSHOT** (Simulated)
• Nifty: 19,500 (+0.5%)
• Status: OPEN
• Trend: BULLISH
• Market Strength: MODERATE

🎯 **RECOMMENDATION** ({option_type})
• This is a simplified test response
• Real analysis includes technical indicators
• Live market data integration
• Risk management guidelines

✅ **MCP SERVER IS WORKING!**
        """.strip()
        
        return [TextContent(type="text", text=analysis)]

async def main():
    """Main entry point"""
    logger.info("Starting Simplified Intraday Alpha Assistant MCP Server")
    
    server_instance = SimpleIntradayServer()
    
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server_instance.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="intraday-alpha-assistant",
                    server_version="1.0.0-simple",
                    capabilities=server_instance.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    )
                )
            )
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
