"""
Stock Screener Module
Identifies top performing stocks for intraday trading
"""

import yfinance as yf
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import asyncio

logger = logging.getLogger(__name__)

class StockScreener:
    def __init__(self):
        # Nifty 50 stocks for screening
        self.nifty_50_stocks = [
            "R<PERSON><PERSON>NC<PERSON>", "TCS", "HDFC<PERSON>N<PERSON>", "INFY", "HINDUN<PERSON><PERSON>", "ICICIBAN<PERSON>", 
            "KOTAKBANK", "BHARTIARTL", "ITC", "SBIN", "BAJFINANCE", "ASIANPAINT",
            "MARUTI", "AXISBANK", "LT", "HC<PERSON><PERSON><PERSON>", "WIP<PERSON>", "<PERSON><PERSON><PERSON><PERSON>MC<PERSON>",
            "NESTLEIND", "TITAN", "SUNPHARMA", "ONGC", "NTPC", "POWERGRID",
            "BAJAJFINSV", "M&M", "TECHM", "TATAMOTORS", "COALINDIA", "HDFCLIFE",
            "GRASIM", "AD<PERSON>IPORTS", "JSWSTEEL", "IND<PERSON><PERSON><PERSON><PERSON><PERSON>", "BR<PERSON>ANNIA",
            "CIPLA", "DRREDDY", "EICHERMOT", "UPL", "APOLLOHOSP", "DIVISLAB",
            "HINDALCO", "HEROMOTOCO", "BAJAJ-AUTO", "TATACONSUM", "SBILIFE",
            "BPCL", "TATASTEEL", "IOC", "ADANIENT"
        ]
        
        # Sector mapping for analysis
        self.sector_mapping = {
            "Banking": ["HDFCBANK", "ICICIBANK", "KOTAKBANK", "SBIN", "AXISBANK", "INDUSINDBK"],
            "IT": ["TCS", "INFY", "HCLTECH", "WIPRO", "TECHM"],
            "Auto": ["MARUTI", "TATAMOTORS", "EICHERMOT", "HEROMOTOCO", "BAJAJ-AUTO", "M&M"],
            "Pharma": ["SUNPHARMA", "CIPLA", "DRREDDY", "APOLLOHOSP", "DIVISLAB"],
            "FMCG": ["HINDUNILVR", "ITC", "NESTLEIND", "BRITANNIA", "TATACONSUM"],
            "Energy": ["RELIANCE", "ONGC", "BPCL", "IOC", "COALINDIA"],
            "Metals": ["TATASTEEL", "JSWSTEEL", "HINDALCO", "COALINDIA"],
            "Telecom": ["BHARTIARTL"],
            "Finance": ["BAJFINANCE", "BAJAJFINSV", "HDFCLIFE", "SBILIFE"]
        }
    
    async def screen_stocks(self, screen_type: str = "both", 
                          sector: Optional[str] = None) -> str:
        """Screen stocks for strongest/weakest performers"""
        try:
            # Get stock list to screen
            if sector and sector in self.sector_mapping:
                stocks_to_screen = self.sector_mapping[sector]
                screen_title = f"{sector} Sector"
            else:
                stocks_to_screen = self.nifty_50_stocks[:20]  # Limit to 20 for performance
                screen_title = "Nifty 50"
            
            # Fetch stock data
            stock_data = await self._fetch_stock_data(stocks_to_screen)
            
            # Analyze and rank stocks
            analyzed_stocks = self._analyze_stocks(stock_data)
            
            # Generate recommendations
            if screen_type == "strongest":
                return self._format_strongest_stocks(analyzed_stocks, screen_title)
            elif screen_type == "weakest":
                return self._format_weakest_stocks(analyzed_stocks, screen_title)
            else:  # both
                return self._format_both_stocks(analyzed_stocks, screen_title)
                
        except Exception as e:
            logger.error(f"Error screening stocks: {e}")
            return f"❌ Error screening stocks: {str(e)}"
    
    async def _fetch_stock_data(self, stock_symbols: List[str]) -> Dict[str, Any]:
        """Fetch real-time data for multiple stocks"""
        stock_data = {}
        
        # Process stocks in batches to avoid overwhelming the API
        batch_size = 5
        for i in range(0, len(stock_symbols), batch_size):
            batch = stock_symbols[i:i + batch_size]
            
            for symbol in batch:
                try:
                    ticker_symbol = f"{symbol}.NS"
                    stock = yf.Ticker(ticker_symbol)
                    
                    # Get intraday data
                    hist = stock.history(period="1d", interval="5m")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        open_price = hist['Open'].iloc[0]
                        high_price = hist['High'].max()
                        low_price = hist['Low'].min()
                        volume = hist['Volume'].sum()
                        
                        # Calculate metrics
                        change = current_price - open_price
                        change_percent = (change / open_price) * 100
                        
                        # Calculate intraday momentum
                        momentum_score = self._calculate_momentum_score(hist)
                        
                        # Calculate volume strength
                        volume_strength = self._calculate_volume_strength(hist)
                        
                        stock_data[symbol] = {
                            "current_price": round(current_price, 2),
                            "open": round(open_price, 2),
                            "high": round(high_price, 2),
                            "low": round(low_price, 2),
                            "change": round(change, 2),
                            "change_percent": round(change_percent, 2),
                            "volume": int(volume),
                            "momentum_score": momentum_score,
                            "volume_strength": volume_strength,
                            "historical_data": hist.tail(12).to_dict('records')  # Last 1 hour
                        }
                        
                except Exception as e:
                    logger.error(f"Error fetching data for {symbol}: {e}")
                    continue
            
            # Small delay between batches
            await asyncio.sleep(0.1)
        
        return stock_data
    
    def _calculate_momentum_score(self, hist_data: pd.DataFrame) -> float:
        """Calculate momentum score based on price action"""
        try:
            if len(hist_data) < 6:
                return 0.0
            
            # Calculate short-term momentum (last 30 minutes vs previous 30 minutes)
            recent_data = hist_data.tail(6)  # Last 30 minutes (6 * 5min)
            previous_data = hist_data.iloc[-12:-6]  # Previous 30 minutes
            
            if len(previous_data) == 0:
                return 0.0
            
            recent_avg = recent_data['Close'].mean()
            previous_avg = previous_data['Close'].mean()
            
            momentum = ((recent_avg - previous_avg) / previous_avg) * 100
            
            # Normalize to 0-10 scale
            momentum_score = max(0, min(10, 5 + momentum))
            
            return round(momentum_score, 2)
            
        except Exception as e:
            logger.error(f"Error calculating momentum score: {e}")
            return 5.0
    
    def _calculate_volume_strength(self, hist_data: pd.DataFrame) -> float:
        """Calculate volume strength score"""
        try:
            if len(hist_data) < 6:
                return 0.0
            
            # Compare recent volume with average
            recent_volume = hist_data.tail(6)['Volume'].sum()
            avg_volume = hist_data['Volume'].mean() * 6
            
            if avg_volume == 0:
                return 5.0
            
            volume_ratio = recent_volume / avg_volume
            
            # Convert to 0-10 scale
            if volume_ratio >= 2.0:
                return 10.0
            elif volume_ratio >= 1.5:
                return 8.0
            elif volume_ratio >= 1.2:
                return 7.0
            elif volume_ratio >= 0.8:
                return 5.0
            else:
                return 3.0
                
        except Exception as e:
            logger.error(f"Error calculating volume strength: {e}")
            return 5.0
    
    def _analyze_stocks(self, stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze and rank stocks"""
        analyzed_stocks = []
        
        for symbol, data in stock_data.items():
            try:
                # Calculate overall strength score
                price_strength = self._calculate_price_strength(data)
                momentum_strength = data.get('momentum_score', 5.0)
                volume_strength = data.get('volume_strength', 5.0)
                
                # Weighted overall score
                overall_score = (
                    price_strength * 0.4 +
                    momentum_strength * 0.3 +
                    volume_strength * 0.3
                )
                
                analyzed_stocks.append({
                    "symbol": symbol,
                    "current_price": data['current_price'],
                    "change_percent": data['change_percent'],
                    "volume": data['volume'],
                    "price_strength": price_strength,
                    "momentum_score": momentum_strength,
                    "volume_strength": volume_strength,
                    "overall_score": round(overall_score, 2),
                    "recommendation": self._get_stock_recommendation(overall_score, data['change_percent'])
                })
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort by overall score
        analyzed_stocks.sort(key=lambda x: x['overall_score'], reverse=True)
        
        return analyzed_stocks
    
    def _calculate_price_strength(self, data: Dict[str, Any]) -> float:
        """Calculate price strength based on change and position in range"""
        try:
            change_percent = data['change_percent']
            current_price = data['current_price']
            high = data['high']
            low = data['low']
            
            # Position in day's range
            if high != low:
                range_position = (current_price - low) / (high - low)
            else:
                range_position = 0.5
            
            # Combine change percent and range position
            price_strength = (abs(change_percent) * 2) + (range_position * 3)
            
            # Adjust for direction (positive for gains, negative for losses)
            if change_percent > 0:
                price_strength += 2
            else:
                price_strength = max(0, price_strength - 2)
            
            return min(10, max(0, price_strength))
            
        except Exception as e:
            logger.error(f"Error calculating price strength: {e}")
            return 5.0
    
    def _get_stock_recommendation(self, overall_score: float, change_percent: float) -> str:
        """Get recommendation based on score and performance"""
        if overall_score >= 8 and change_percent > 0:
            return "STRONG BUY"
        elif overall_score >= 7 and change_percent > 0:
            return "BUY"
        elif overall_score >= 6:
            return "WATCH"
        elif overall_score <= 3 and change_percent < 0:
            return "STRONG SELL"
        elif overall_score <= 4 and change_percent < 0:
            return "SELL"
        else:
            return "NEUTRAL"
    
    def _format_strongest_stocks(self, analyzed_stocks: List[Dict], screen_title: str) -> str:
        """Format strongest stocks output"""
        if not analyzed_stocks:
            return f"❌ No data available for {screen_title} stocks"
        
        top_3 = analyzed_stocks[:3]
        
        result = f"""
💪 **TOP 3 STRONGEST STOCKS** ({screen_title})
*Updated: {datetime.now().strftime('%H:%M:%S')}*

"""
        
        for i, stock in enumerate(top_3, 1):
            result += f"""
**{i}. {stock['symbol']}** 
• Price: ₹{stock['current_price']} ({stock['change_percent']:+.2f}%)
• Score: {stock['overall_score']}/10
• Momentum: {stock['momentum_score']}/10
• Volume: {stock['volume_strength']}/10
• Action: {stock['recommendation']}

"""
        
        result += """
🎯 **TRADING STRATEGY**:
• Look for continuation patterns
• Enter on pullbacks to support
• Set stop-loss below recent swing low
• Target next resistance levels
        """.strip()
        
        return result
    
    def _format_weakest_stocks(self, analyzed_stocks: List[Dict], screen_title: str) -> str:
        """Format weakest stocks output"""
        if not analyzed_stocks:
            return f"❌ No data available for {screen_title} stocks"
        
        # Get bottom 3 (weakest)
        bottom_3 = analyzed_stocks[-3:][::-1]  # Reverse to show weakest first
        
        result = f"""
📉 **TOP 3 WEAKEST STOCKS** ({screen_title})
*Updated: {datetime.now().strftime('%H:%M:%S')}*

"""
        
        for i, stock in enumerate(bottom_3, 1):
            result += f"""
**{i}. {stock['symbol']}** 
• Price: ₹{stock['current_price']} ({stock['change_percent']:+.2f}%)
• Score: {stock['overall_score']}/10
• Momentum: {stock['momentum_score']}/10
• Volume: {stock['volume_strength']}/10
• Action: {stock['recommendation']}

"""
        
        result += """
🎯 **TRADING STRATEGY**:
• Look for breakdown patterns
• Consider put options on bounces
• Wait for volume confirmation
• Set tight stop-losses
        """.strip()
        
        return result
    
    def _format_both_stocks(self, analyzed_stocks: List[Dict], screen_title: str) -> str:
        """Format both strongest and weakest stocks"""
        if not analyzed_stocks:
            return f"❌ No data available for {screen_title} stocks"
        
        strongest = self._format_strongest_stocks(analyzed_stocks, screen_title)
        weakest = self._format_weakest_stocks(analyzed_stocks, screen_title)
        
        return f"{strongest}\n\n{'='*50}\n\n{weakest}"
