"""
Data Fetcher <PERSON><PERSON>le
Handles real-time data collection from various sources
"""

import asyncio
import httpx
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    def __init__(self):
        self.session = httpx.AsyncClient(timeout=30.0)
        self.nse_base_url = "https://www.nseindia.com/api"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    
    async def get_nifty_data(self) -> Dict[str, Any]:
        """Get current Nifty 50 index data"""
        try:
            # Using yfinance for reliable data
            nifty = yf.Ticker("^NSEI")
            
            # Get current price and basic info
            info = nifty.info
            hist = nifty.history(period="1d", interval="1m")
            
            if hist.empty:
                raise ValueError("No data available")
            
            current_price = hist['Close'].iloc[-1]
            open_price = hist['Open'].iloc[0]
            high_price = hist['High'].max()
            low_price = hist['Low'].min()
            volume = hist['Volume'].sum()
            
            # Calculate change
            change = current_price - open_price
            change_percent = (change / open_price) * 100
            
            return {
                "symbol": "NIFTY 50",
                "current_price": round(current_price, 2),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "change": round(change, 2),
                "change_percent": round(change_percent, 2),
                "volume": int(volume),
                "timestamp": datetime.now().isoformat(),
                "market_status": self._get_market_status(),
                "historical_data": hist.tail(50).to_dict('records')  # Last 50 minutes
            }
            
        except Exception as e:
            logger.error(f"Error fetching Nifty data: {e}")
            return self._get_fallback_nifty_data()
    
    async def get_options_chain(self, expiry_date: Optional[str] = None) -> Dict[str, Any]:
        """Get Nifty options chain data"""
        try:
            # If no expiry specified, get nearest Thursday
            if not expiry_date:
                expiry_date = self._get_nearest_thursday()
            
            # For demo purposes, we'll simulate options data
            # In production, you'd integrate with NSE API or broker API
            nifty_data = await self.get_nifty_data()
            current_price = nifty_data['current_price']
            
            options_chain = self._generate_options_chain(current_price, expiry_date)
            
            return {
                "underlying": "NIFTY",
                "underlying_price": current_price,
                "expiry_date": expiry_date,
                "options": options_chain,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error fetching options chain: {e}")
            return {"error": str(e)}
    
    async def get_stock_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Get data for multiple stocks"""
        stock_data = {}
        
        for symbol in symbols:
            try:
                # Add .NS suffix for NSE stocks
                ticker_symbol = f"{symbol}.NS" if not symbol.endswith('.NS') else symbol
                stock = yf.Ticker(ticker_symbol)
                
                hist = stock.history(period="1d", interval="5m")
                if not hist.empty:
                    current_price = hist['Close'].iloc[-1]
                    open_price = hist['Open'].iloc[0]
                    change = current_price - open_price
                    change_percent = (change / open_price) * 100
                    
                    stock_data[symbol] = {
                        "current_price": round(current_price, 2),
                        "open": round(open_price, 2),
                        "high": round(hist['High'].max(), 2),
                        "low": round(hist['Low'].min(), 2),
                        "change": round(change, 2),
                        "change_percent": round(change_percent, 2),
                        "volume": int(hist['Volume'].sum()),
                        "historical_data": hist.tail(20).to_dict('records')
                    }
                    
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
                stock_data[symbol] = {"error": str(e)}
        
        return stock_data
    
    def _get_market_status(self) -> str:
        """Determine if market is open"""
        now = datetime.now()
        
        # Check if it's a weekday (Monday = 0, Sunday = 6)
        if now.weekday() >= 5:  # Saturday or Sunday
            return "closed"
        
        # Market hours: 9:15 AM to 3:30 PM IST
        market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
        market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)
        
        if market_open <= now <= market_close:
            return "open"
        elif now < market_open:
            return "pre_market"
        else:
            return "closed"
    
    def _get_nearest_thursday(self) -> str:
        """Get the nearest Thursday for options expiry"""
        today = datetime.now().date()
        days_ahead = 3 - today.weekday()  # Thursday is 3
        
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        
        nearest_thursday = today + timedelta(days=days_ahead)
        return nearest_thursday.strftime("%Y-%m-%d")
    
    def _generate_options_chain(self, spot_price: float, expiry_date: str) -> List[Dict]:
        """Generate simulated options chain data"""
        options = []
        
        # Generate strikes around current price
        base_strike = int(spot_price / 50) * 50  # Round to nearest 50
        
        for i in range(-10, 11):  # 21 strikes total
            strike = base_strike + (i * 50)
            
            # Simulate option prices (simplified Black-Scholes approximation)
            ce_price = max(0.1, spot_price - strike + 50) if spot_price > strike else max(0.1, 50 - abs(spot_price - strike) * 0.1)
            pe_price = max(0.1, strike - spot_price + 50) if strike > spot_price else max(0.1, 50 - abs(strike - spot_price) * 0.1)
            
            options.append({
                "strike": strike,
                "ce_price": round(ce_price, 2),
                "pe_price": round(pe_price, 2),
                "ce_volume": int(abs(hash(f"CE{strike}")) % 10000),
                "pe_volume": int(abs(hash(f"PE{strike}")) % 10000),
                "ce_oi": int(abs(hash(f"CE_OI{strike}")) % 50000),
                "pe_oi": int(abs(hash(f"PE_OI{strike}")) % 50000),
                "ce_iv": round(15 + abs(hash(f"CE_IV{strike}")) % 20, 2),
                "pe_iv": round(15 + abs(hash(f"PE_IV{strike}")) % 20, 2)
            })
        
        return options
    
    def _get_fallback_nifty_data(self) -> Dict[str, Any]:
        """Fallback data when API fails"""
        return {
            "symbol": "NIFTY 50",
            "current_price": 19500.0,
            "open": 19450.0,
            "high": 19550.0,
            "low": 19400.0,
            "change": 50.0,
            "change_percent": 0.26,
            "volume": 1000000,
            "timestamp": datetime.now().isoformat(),
            "market_status": "closed",
            "error": "Using fallback data - API unavailable"
        }
    
    async def close(self):
        """Close the HTTP session"""
        await self.session.aclose()
