"""
Technical Analysis Module
Provides technical indicators and market analysis
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    def __init__(self):
        self.indicators = {}
    
    async def analyze_nifty(self, nifty_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive technical analysis on Nifty data"""
        try:
            # Convert historical data to DataFrame
            if 'historical_data' not in nifty_data or not nifty_data['historical_data']:
                return self._get_basic_analysis(nifty_data)
            
            df = pd.DataFrame(nifty_data['historical_data'])
            
            if df.empty or len(df) < 20:
                return self._get_basic_analysis(nifty_data)
            
            # Ensure proper column names
            df.columns = [col.title() for col in df.columns]
            
            # Calculate technical indicators
            analysis = {
                "current_price": nifty_data['current_price'],
                "trend_analysis": self._analyze_trend(df),
                "momentum_indicators": self._calculate_momentum_indicators(df),
                "volatility_indicators": self._calculate_volatility_indicators(df),
                "support_resistance": self._find_support_resistance(df),
                "volume_analysis": self._analyze_volume(df),
                "signals": self._generate_signals(df),
                "market_strength": self._calculate_market_strength(df),
                "timestamp": datetime.now().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in technical analysis: {e}")
            return self._get_basic_analysis(nifty_data)
    
    def _analyze_trend(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze price trend using multiple methods"""
        try:
            # Simple Moving Averages
            df['SMA_9'] = ta.trend.sma_indicator(df['Close'], window=9)
            df['SMA_21'] = ta.trend.sma_indicator(df['Close'], window=21)
            
            # Exponential Moving Averages
            df['EMA_9'] = ta.trend.ema_indicator(df['Close'], window=9)
            df['EMA_21'] = ta.trend.ema_indicator(df['Close'], window=21)
            
            current_price = df['Close'].iloc[-1]
            sma_9 = df['SMA_9'].iloc[-1]
            sma_21 = df['SMA_21'].iloc[-1]
            ema_9 = df['EMA_9'].iloc[-1]
            ema_21 = df['EMA_21'].iloc[-1]
            
            # Determine trend direction
            trend_signals = []
            if current_price > sma_9 > sma_21:
                trend_signals.append("bullish_sma")
            elif current_price < sma_9 < sma_21:
                trend_signals.append("bearish_sma")
            
            if current_price > ema_9 > ema_21:
                trend_signals.append("bullish_ema")
            elif current_price < ema_9 < ema_21:
                trend_signals.append("bearish_ema")
            
            # Overall trend strength
            bullish_count = sum(1 for signal in trend_signals if "bullish" in signal)
            bearish_count = sum(1 for signal in trend_signals if "bearish" in signal)
            
            if bullish_count > bearish_count:
                overall_trend = "bullish"
                trend_strength = bullish_count / len(trend_signals) if trend_signals else 0.5
            elif bearish_count > bullish_count:
                overall_trend = "bearish"
                trend_strength = bearish_count / len(trend_signals) if trend_signals else 0.5
            else:
                overall_trend = "sideways"
                trend_strength = 0.5
            
            return {
                "direction": overall_trend,
                "strength": round(trend_strength, 2),
                "sma_9": round(sma_9, 2) if not pd.isna(sma_9) else None,
                "sma_21": round(sma_21, 2) if not pd.isna(sma_21) else None,
                "ema_9": round(ema_9, 2) if not pd.isna(ema_9) else None,
                "ema_21": round(ema_21, 2) if not pd.isna(ema_21) else None,
                "signals": trend_signals
            }
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {"direction": "unknown", "strength": 0.5, "signals": []}
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate momentum-based indicators"""
        try:
            # RSI
            df['RSI'] = ta.momentum.rsi(df['Close'], window=14)
            
            # MACD
            macd_line = ta.trend.macd(df['Close'])
            macd_signal = ta.trend.macd_signal(df['Close'])
            macd_histogram = ta.trend.macd_diff(df['Close'])
            
            # Stochastic
            stoch_k = ta.momentum.stoch(df['High'], df['Low'], df['Close'])
            stoch_d = ta.momentum.stoch_signal(df['High'], df['Low'], df['Close'])
            
            current_rsi = df['RSI'].iloc[-1]
            current_macd = macd_line.iloc[-1]
            current_macd_signal = macd_signal.iloc[-1]
            current_macd_hist = macd_histogram.iloc[-1]
            current_stoch_k = stoch_k.iloc[-1]
            current_stoch_d = stoch_d.iloc[-1]
            
            # Generate momentum signals
            momentum_signals = []
            
            # RSI signals
            if current_rsi > 70:
                momentum_signals.append("rsi_overbought")
            elif current_rsi < 30:
                momentum_signals.append("rsi_oversold")
            elif 40 <= current_rsi <= 60:
                momentum_signals.append("rsi_neutral")
            
            # MACD signals
            if current_macd > current_macd_signal and current_macd_hist > 0:
                momentum_signals.append("macd_bullish")
            elif current_macd < current_macd_signal and current_macd_hist < 0:
                momentum_signals.append("macd_bearish")
            
            # Stochastic signals
            if current_stoch_k > 80 and current_stoch_d > 80:
                momentum_signals.append("stoch_overbought")
            elif current_stoch_k < 20 and current_stoch_d < 20:
                momentum_signals.append("stoch_oversold")
            
            return {
                "rsi": round(current_rsi, 2) if not pd.isna(current_rsi) else None,
                "macd": {
                    "line": round(current_macd, 2) if not pd.isna(current_macd) else None,
                    "signal": round(current_macd_signal, 2) if not pd.isna(current_macd_signal) else None,
                    "histogram": round(current_macd_hist, 2) if not pd.isna(current_macd_hist) else None
                },
                "stochastic": {
                    "k": round(current_stoch_k, 2) if not pd.isna(current_stoch_k) else None,
                    "d": round(current_stoch_d, 2) if not pd.isna(current_stoch_d) else None
                },
                "signals": momentum_signals
            }
            
        except Exception as e:
            logger.error(f"Error calculating momentum indicators: {e}")
            return {"signals": []}
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate volatility-based indicators"""
        try:
            # Bollinger Bands
            bb_high = ta.volatility.bollinger_hband(df['Close'])
            bb_low = ta.volatility.bollinger_lband(df['Close'])
            bb_mid = ta.volatility.bollinger_mavg(df['Close'])
            
            # Average True Range
            atr = ta.volatility.average_true_range(df['High'], df['Low'], df['Close'])
            
            current_price = df['Close'].iloc[-1]
            current_bb_high = bb_high.iloc[-1]
            current_bb_low = bb_low.iloc[-1]
            current_bb_mid = bb_mid.iloc[-1]
            current_atr = atr.iloc[-1]
            
            # Bollinger Band signals
            volatility_signals = []
            if current_price >= current_bb_high:
                volatility_signals.append("bb_upper_breach")
            elif current_price <= current_bb_low:
                volatility_signals.append("bb_lower_breach")
            elif abs(current_price - current_bb_mid) / current_bb_mid < 0.005:
                volatility_signals.append("bb_middle_line")
            
            # Calculate volatility percentage
            bb_width = ((current_bb_high - current_bb_low) / current_bb_mid) * 100
            
            return {
                "bollinger_bands": {
                    "upper": round(current_bb_high, 2) if not pd.isna(current_bb_high) else None,
                    "middle": round(current_bb_mid, 2) if not pd.isna(current_bb_mid) else None,
                    "lower": round(current_bb_low, 2) if not pd.isna(current_bb_low) else None,
                    "width_percent": round(bb_width, 2) if not pd.isna(bb_width) else None
                },
                "atr": round(current_atr, 2) if not pd.isna(current_atr) else None,
                "signals": volatility_signals
            }
            
        except Exception as e:
            logger.error(f"Error calculating volatility indicators: {e}")
            return {"signals": []}
    
    def _find_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Identify support and resistance levels"""
        try:
            # Simple pivot point calculation
            high_prices = df['High'].tail(20)
            low_prices = df['Low'].tail(20)
            
            # Find local maxima and minima
            resistance_levels = []
            support_levels = []
            
            for i in range(2, len(high_prices) - 2):
                if (high_prices.iloc[i] > high_prices.iloc[i-1] and 
                    high_prices.iloc[i] > high_prices.iloc[i+1] and
                    high_prices.iloc[i] > high_prices.iloc[i-2] and 
                    high_prices.iloc[i] > high_prices.iloc[i+2]):
                    resistance_levels.append(high_prices.iloc[i])
            
            for i in range(2, len(low_prices) - 2):
                if (low_prices.iloc[i] < low_prices.iloc[i-1] and 
                    low_prices.iloc[i] < low_prices.iloc[i+1] and
                    low_prices.iloc[i] < low_prices.iloc[i-2] and 
                    low_prices.iloc[i] < low_prices.iloc[i+2]):
                    support_levels.append(low_prices.iloc[i])
            
            # Get the most relevant levels
            current_price = df['Close'].iloc[-1]
            
            # Find nearest support and resistance
            resistance_above = [r for r in resistance_levels if r > current_price]
            support_below = [s for s in support_levels if s < current_price]
            
            nearest_resistance = min(resistance_above) if resistance_above else None
            nearest_support = max(support_below) if support_below else None
            
            return {
                "nearest_support": round(nearest_support, 2) if nearest_support else None,
                "nearest_resistance": round(nearest_resistance, 2) if nearest_resistance else None,
                "all_support_levels": [round(s, 2) for s in sorted(support_levels, reverse=True)[:3]],
                "all_resistance_levels": [round(r, 2) for r in sorted(resistance_levels)[:3]]
            }
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {e}")
            return {}
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns"""
        try:
            # Volume moving average
            df['Volume_MA'] = df['Volume'].rolling(window=10).mean()
            
            current_volume = df['Volume'].iloc[-1]
            avg_volume = df['Volume_MA'].iloc[-1]
            
            volume_signals = []
            if current_volume > avg_volume * 1.5:
                volume_signals.append("high_volume")
            elif current_volume < avg_volume * 0.5:
                volume_signals.append("low_volume")
            
            return {
                "current_volume": int(current_volume),
                "average_volume": int(avg_volume) if not pd.isna(avg_volume) else None,
                "volume_ratio": round(current_volume / avg_volume, 2) if not pd.isna(avg_volume) and avg_volume > 0 else None,
                "signals": volume_signals
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")
            return {"signals": []}
    
    def _generate_signals(self, df: pd.DataFrame) -> List[str]:
        """Generate overall trading signals"""
        all_signals = []
        
        # Collect signals from all analyses
        trend_analysis = self._analyze_trend(df)
        momentum_analysis = self._calculate_momentum_indicators(df)
        volatility_analysis = self._calculate_volatility_indicators(df)
        volume_analysis = self._analyze_volume(df)
        
        all_signals.extend(trend_analysis.get('signals', []))
        all_signals.extend(momentum_analysis.get('signals', []))
        all_signals.extend(volatility_analysis.get('signals', []))
        all_signals.extend(volume_analysis.get('signals', []))
        
        return all_signals
    
    def _calculate_market_strength(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate overall market strength score"""
        try:
            bullish_signals = 0
            bearish_signals = 0
            total_signals = 0
            
            # Analyze all signals
            signals = self._generate_signals(df)
            
            for signal in signals:
                total_signals += 1
                if any(word in signal.lower() for word in ['bullish', 'oversold', 'support']):
                    bullish_signals += 1
                elif any(word in signal.lower() for word in ['bearish', 'overbought', 'resistance']):
                    bearish_signals += 1
            
            if total_signals == 0:
                strength_score = 0.5
                strength_label = "neutral"
            else:
                strength_score = bullish_signals / total_signals
                if strength_score >= 0.7:
                    strength_label = "strong_bullish"
                elif strength_score >= 0.6:
                    strength_label = "bullish"
                elif strength_score >= 0.4:
                    strength_label = "neutral"
                elif strength_score >= 0.3:
                    strength_label = "bearish"
                else:
                    strength_label = "strong_bearish"
            
            return {
                "score": round(strength_score, 2),
                "label": strength_label,
                "bullish_signals": bullish_signals,
                "bearish_signals": bearish_signals,
                "total_signals": total_signals
            }
            
        except Exception as e:
            logger.error(f"Error calculating market strength: {e}")
            return {"score": 0.5, "label": "neutral"}
    
    def _get_basic_analysis(self, nifty_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback analysis when insufficient data"""
        current_price = nifty_data['current_price']
        open_price = nifty_data['open']
        change_percent = nifty_data['change_percent']
        
        # Basic trend determination
        if change_percent > 1:
            trend = "strong_bullish"
        elif change_percent > 0.5:
            trend = "bullish"
        elif change_percent > -0.5:
            trend = "sideways"
        elif change_percent > -1:
            trend = "bearish"
        else:
            trend = "strong_bearish"
        
        return {
            "current_price": current_price,
            "trend_analysis": {"direction": trend, "strength": abs(change_percent) / 2},
            "basic_signals": [f"price_change_{change_percent:.1f}%"],
            "market_strength": {"score": 0.5, "label": "insufficient_data"},
            "timestamp": datetime.now().isoformat()
        }
    
    async def analyze_market_sentiment(self, timeframe: str = "15m") -> str:
        """Analyze overall market sentiment"""
        try:
            # This would integrate with broader market data
            # For now, return a formatted sentiment analysis
            return f"""
🎯 **MARKET SENTIMENT ANALYSIS** ({timeframe.upper()})

**Overall Sentiment**: Cautiously Optimistic
**Trend Direction**: Sideways with Bullish Bias
**Volatility**: Moderate
**Volume**: Above Average

**Key Observations**:
• Market showing consolidation pattern
• Support holding at key levels
• Momentum indicators mixed
• Volume suggests institutional interest

**Recommendation**: Wait for clear breakout signals
            """.strip()
            
        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return "Error analyzing market sentiment"
    
    async def get_current_indicators(self) -> Dict[str, Any]:
        """Get current technical indicators summary"""
        return {
            "last_updated": datetime.now().isoformat(),
            "indicators_available": [
                "RSI", "MACD", "Bollinger Bands", "Moving Averages",
                "Stochastic", "ATR", "Support/Resistance"
            ],
            "timeframes_supported": ["1m", "5m", "15m", "1h", "1d"],
            "status": "active"
        }
