{"// Option 1: Full Python path (RECOMMENDED)": "", "mcpServers_option1": {"intraday-alpha-assistant": {"command": "/Users/<USER>/.pyenv/versions/3.10.13/bin/python3", "args": ["/Users/<USER>/Documents/augment-projects/option-buy-mcp/src/server.py"], "env": {"PYTHONPATH": "/Users/<USER>/Documents/augment-projects/option-buy-mcp"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}, "// Option 2: Using python3 command": "", "mcpServers_option2": {"intraday-alpha-assistant": {"command": "python3", "args": ["/Users/<USER>/Documents/augment-projects/option-buy-mcp/src/server.py"], "env": {"PYTHONPATH": "/Users/<USER>/Documents/augment-projects/option-buy-mcp", "PATH": "/Users/<USER>/.pyenv/versions/3.10.13/bin:/usr/local/bin:/usr/bin:/bin"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}, "// Option 3: Using shell wrapper": "", "mcpServers_option3": {"intraday-alpha-assistant": {"command": "/bin/bash", "args": ["-c", "cd /Users/<USER>/Documents/augment-projects/option-buy-mcp && /Users/<USER>/.pyenv/versions/3.10.13/bin/python3 src/server.py"], "env": {}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}}