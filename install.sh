#!/bin/bash

# Intraday Alpha Assistant MCP Installation Script

echo "🚀 Installing Intraday Alpha Assistant MCP..."

# Check Python version
python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Run tests
echo "🧪 Running tests..."
python3 test_mcp.py

if [ $? -ne 0 ]; then
    echo "⚠️  Some tests failed, but installation can continue"
fi

# Get current directory
CURRENT_DIR=$(pwd)

# Create Claude Desktop config
echo "⚙️  Setting up Claude Desktop configuration..."

# Detect OS and set config path
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CONFIG_DIR="$HOME/Library/Application Support/Claude"
    CONFIG_FILE="$CONFIG_DIR/claude_desktop_config.json"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    CONFIG_DIR="$APPDATA/Claude"
    CONFIG_FILE="$CONFIG_DIR/claude_desktop_config.json"
else
    # Linux
    CONFIG_DIR="$HOME/.config/claude"
    CONFIG_FILE="$CONFIG_DIR/claude_desktop_config.json"
fi

# Create config directory if it doesn't exist
mkdir -p "$CONFIG_DIR"

# Generate config content
cat > "$CONFIG_FILE" << EOF
{
  "mcpServers": {
    "intraday-alpha-assistant": {
      "command": "python3",
      "args": ["$CURRENT_DIR/src/server.py"],
      "env": {
        "PYTHONPATH": "$CURRENT_DIR"
      }
    }
  }
}
EOF

echo "✅ Claude Desktop configuration created at: $CONFIG_FILE"

# Make server executable
chmod +x src/server.py

echo ""
echo "🎉 Installation Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Restart Claude Desktop application"
echo "2. Test the MCP by asking: 'Analyze Nifty options for today'"
echo "3. Use commands like:"
echo "   - 'Show me the strongest stocks today'"
echo "   - 'Recommend strike price for Nifty CE'"
echo "   - 'Calculate position size for 19500 CE with ₹10000'"
echo ""
echo "⚠️  Important Notes:"
echo "- This tool is for educational purposes only"
echo "- Always use proper risk management"
echo "- Not financial advice - do your own research"
echo ""
echo "🔧 Troubleshooting:"
echo "- If MCP doesn't load, check Claude Desktop logs"
echo "- Ensure Python path is correct in config"
echo "- Run 'python3 test_mcp.py' to verify functionality"
echo ""
echo "Happy Trading! 🚀📈"
