# 🚀 Intraday Alpha Assistant - Installation Guide

## Quick Setup for <PERSON>

### Step 1: Install Dependencies
```bash
cd /Users/<USER>/Documents/augment-projects/option-buy-mcp
pip install -r requirements.txt
```

### Step 2: Test the Installation
```bash
python3 test_mcp.py
```
You should see all tests passing with ✅ marks.

### Step 3: Configure <PERSON>

**For macOS:**
1. Open: `~/Library/Application Support/Claude/claude_desktop_config.json`
2. Add this configuration:

```json
{
  "mcpServers": {
    "intraday-alpha-assistant": {
      "command": "python3",
      "args": ["/Users/<USER>/Documents/augment-projects/option-buy-mcp/src/server.py"],
      "env": {
        "PYTHONPATH": "/Users/<USER>/Documents/augment-projects/option-buy-mcp"
      }
    }
  }
}
```

**For Windows:**
1. Open: `%APPDATA%\Claude\claude_desktop_config.json`
2. Use the same configuration but adjust the path

### Step 4: Restart <PERSON>
Close and reopen <PERSON> completely.

### Step 5: Test with <PERSON>
Try these commands in Claude Desktop:

1. **"Analyze Nifty options for today - should I buy CE or PE?"**
2. **"Show me the top 3 strongest stocks today"**
3. **"Recommend strike price for Nifty CE with moderate risk"**
4. **"Calculate position size for 19500 CE with ₹10,000"**

## 🎯 Expected Output

When you ask Claude to analyze Nifty options, you should get something like:

```
🚀 INTRADAY ALPHA ASSISTANT - 26 Jun 2025, 10:56

📊 MARKET SNAPSHOT
• Nifty: 25,328.7 (+0.23%)
• Status: OPEN
• Trend: BULLISH (Strength: 0.7)
• Market Strength: BULLISH

🟢 BULLISH SIGNAL DETECTED

RECOMMENDED ACTION: BUY CALL OPTIONS (CE)
• Best Strike: 25350 CE (Score: 8.2/10)
• Strategy: Momentum-based call buying
• Entry: On pullback to support or breakout confirmation
• Target: Next resistance level
• Stop Loss: 40% of premium paid

⚠️ RISK MANAGEMENT
• Never risk more than 2-5% of capital per trade
• Set stop-loss at 30-50% of premium paid
• Book profits at 50-100% gains
• Avoid trading in last 30 minutes
```

## 🔧 Troubleshooting

### Issue: MCP Server Not Loading / "spawn python ENOENT"
**Solution:**
1. **Use full Python path** (RECOMMENDED):
   ```json
   "command": "/Users/<USER>/.pyenv/versions/3.10.13/bin/python3"
   ```

2. **Alternative: Use shell wrapper**:
   ```json
   "command": "/Users/<USER>/Documents/augment-projects/option-buy-mcp/start_mcp_server.sh"
   ```

3. **Check Python installation**:
   ```bash
   which python3
   /Users/<USER>/.pyenv/versions/3.10.13/bin/python3 --version
   ```

4. **Test server directly**:
   ```bash
   cd /Users/<USER>/Documents/augment-projects/option-buy-mcp
   python3 -c "import src.server; print('OK')"
   ```

### Issue: No Market Data
**Solution:**
1. Check internet connection
2. Verify market hours (9:15 AM - 3:30 PM IST)
3. Try running test script: `python3 test_mcp.py`

### Issue: Import Errors
**Solution:**
1. Ensure PYTHONPATH is set correctly in config
2. Run from project root directory
3. Check Python version (3.8+ required)

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| "Analyze Nifty options" | Get CE/PE recommendations |
| "Show strongest stocks" | Top 3 momentum stocks |
| "Recommend strike price" | Optimal strike selection |
| "Calculate position size" | Risk management levels |
| "Market sentiment analysis" | Overall trend assessment |

## ⚠️ Important Notes

1. **Educational Purpose Only**: This tool is for learning and analysis
2. **Not Financial Advice**: Always do your own research
3. **Risk Management**: Never risk more than you can afford to lose
4. **Paper Trading**: Test strategies before using real money
5. **Market Hours**: Works best during Indian market hours (9:15 AM - 3:30 PM IST)

## 🎉 Success Indicators

✅ All tests pass when running `python3 test_mcp.py`
✅ Claude Desktop shows the MCP server as connected
✅ Commands return formatted analysis with market data
✅ Real-time Nifty prices and technical indicators display
✅ Option recommendations include strike prices and scores

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Run the test script to identify problems
3. Verify your Claude Desktop configuration
4. Ensure Python dependencies are properly installed

Happy Trading! 🚀📈
