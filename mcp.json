{"name": "intraday-alpha-assistant", "version": "1.0.0", "description": "Personalized Nifty Option Buying Assistant with real-time technical analysis", "main": "src/server.py", "tools": [{"name": "analyze_nifty_options", "description": "Analyze Nifty CE/PE options and provide buy/sell recommendations"}, {"name": "get_strike_recommendation", "description": "Get optimal strike price recommendations based on technical analysis"}, {"name": "get_entry_exit_levels", "description": "Calculate ideal entry and exit levels for option trades"}, {"name": "screen_top_stocks", "description": "Identify top 3 strongest/weakest stocks for the day"}, {"name": "get_market_sentiment", "description": "analyze overall market sentiment and trend direction"}], "resources": [{"name": "nifty_data", "description": "Real-time Nifty index and options data"}, {"name": "technical_indicators", "description": "Technical analysis indicators and signals"}]}