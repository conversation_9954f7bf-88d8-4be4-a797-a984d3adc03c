#!/usr/bin/env python3
"""
Test script for Intraday Alpha Assistant MCP
"""

import asyncio
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import DataFetcher
from technical_analysis import TechnicalAnalyzer
from option_analyzer import OptionAnalyzer
from decision_engine import DecisionEngine
from stock_screener import StockScreener

async def test_data_fetcher():
    """Test data fetching functionality"""
    print("🔍 Testing Data Fetcher...")
    
    fetcher = DataFetcher()
    
    try:
        # Test Nifty data
        nifty_data = await fetcher.get_nifty_data()
        print(f"✅ Nifty Data: {nifty_data['current_price']} ({nifty_data['change_percent']:+.2f}%)")
        
        # Test options chain
        options_data = await fetcher.get_options_chain()
        print(f"✅ Options Chain: {len(options_data.get('options', []))} strikes available")
        
        # Test stock data
        stock_data = await fetcher.get_stock_data(["RELIANCE", "TCS", "HDFCBANK"])
        print(f"✅ Stock Data: {len(stock_data)} stocks fetched")
        
    except Exception as e:
        print(f"❌ Data Fetcher Error: {e}")
    
    finally:
        await fetcher.close()

async def test_technical_analysis():
    """Test technical analysis functionality"""
    print("\n📊 Testing Technical Analysis...")
    
    fetcher = DataFetcher()
    analyzer = TechnicalAnalyzer()
    
    try:
        nifty_data = await fetcher.get_nifty_data()
        analysis = await analyzer.analyze_nifty(nifty_data)
        
        trend = analysis.get('trend_analysis', {})
        print(f"✅ Trend Analysis: {trend.get('direction', 'N/A')} (Strength: {trend.get('strength', 0)})")
        
        momentum = analysis.get('momentum_indicators', {})
        print(f"✅ RSI: {momentum.get('rsi', 'N/A')}")
        
        support_resistance = analysis.get('support_resistance', {})
        print(f"✅ Support: {support_resistance.get('nearest_support', 'N/A')}")
        print(f"✅ Resistance: {support_resistance.get('nearest_resistance', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Technical Analysis Error: {e}")
    
    finally:
        await fetcher.close()

async def test_option_analyzer():
    """Test option analysis functionality"""
    print("\n🎯 Testing Option Analyzer...")
    
    fetcher = DataFetcher()
    analyzer = OptionAnalyzer()
    technical_analyzer = TechnicalAnalyzer()
    
    try:
        nifty_data = await fetcher.get_nifty_data()
        options_data = await fetcher.get_options_chain()
        technical_signals = await technical_analyzer.analyze_nifty(nifty_data)
        
        option_analysis = await analyzer.analyze_options(
            options_data, technical_signals, "both"
        )
        
        ce_recs = option_analysis.get('option_recommendations', {}).get('CE', {})
        pe_recs = option_analysis.get('option_recommendations', {}).get('PE', {})
        
        if ce_recs.get('top_recommendations'):
            best_ce = ce_recs['top_recommendations'][0]
            print(f"✅ Best CE Strike: {best_ce['strike']} (Score: {best_ce['overall_score']})")
        
        if pe_recs.get('top_recommendations'):
            best_pe = pe_recs['top_recommendations'][0]
            print(f"✅ Best PE Strike: {best_pe['strike']} (Score: {best_pe['overall_score']})")
        
    except Exception as e:
        print(f"❌ Option Analyzer Error: {e}")
    
    finally:
        await fetcher.close()

async def test_decision_engine():
    """Test decision engine functionality"""
    print("\n🧠 Testing Decision Engine...")
    
    fetcher = DataFetcher()
    technical_analyzer = TechnicalAnalyzer()
    option_analyzer = OptionAnalyzer()
    decision_engine = DecisionEngine()
    
    try:
        nifty_data = await fetcher.get_nifty_data()
        technical_signals = await technical_analyzer.analyze_nifty(nifty_data)
        options_data = await fetcher.get_options_chain()
        option_analysis = await option_analyzer.analyze_options(
            options_data, technical_signals, "both"
        )
        
        recommendation = await decision_engine.generate_recommendation(
            nifty_data, technical_signals, option_analysis
        )
        
        print("✅ Decision Engine Recommendation Generated")
        print("📋 Sample Output:")
        print(recommendation[:200] + "..." if len(recommendation) > 200 else recommendation)
        
    except Exception as e:
        print(f"❌ Decision Engine Error: {e}")
    
    finally:
        await fetcher.close()

async def test_stock_screener():
    """Test stock screening functionality"""
    print("\n📈 Testing Stock Screener...")
    
    screener = StockScreener()
    
    try:
        # Test strongest stocks
        strongest = await screener.screen_stocks("strongest")
        print("✅ Strongest Stocks Screen Generated")
        
        # Test sector screening
        banking_stocks = await screener.screen_stocks("both", "Banking")
        print("✅ Banking Sector Screen Generated")
        
    except Exception as e:
        print(f"❌ Stock Screener Error: {e}")

async def main():
    """Run all tests"""
    print("🚀 Starting Intraday Alpha Assistant MCP Tests\n")
    
    await test_data_fetcher()
    await test_technical_analysis()
    await test_option_analyzer()
    await test_decision_engine()
    await test_stock_screener()
    
    print("\n✅ All tests completed!")
    print("\n🔧 Next Steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Configure Claude Desktop with the provided config")
    print("3. Start the MCP server: python src/server.py")
    print("4. Restart Claude Desktop")
    print("5. Test with: 'Analyze Nifty options for today'")

if __name__ == "__main__":
    asyncio.run(main())
