# 🔧 Troubleshooting Guide - Intraday Alpha Assistant MCP

## 🚨 Current Issue: Server Disconnection

### **Problem Identified:**
The Python MCP server is experiencing import/startup issues, causing disconnections.

### **Immediate Solution:**
I've temporarily configured your Claude Desktop with working MCP servers:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/augment-projects/option-buy-mcp"]
    },
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer"]
    }
  }
}
```

## 🎯 **What You Can Do Right Now:**

1. **Restart Claude Desktop**
2. **Test these working MCP servers:**
   - **Filesystem**: "List files in my project directory"
   - **Puppeteer**: "Take a screenshot of https://www.nseindia.com"

## 🔍 **Root Cause Analysis:**

### **Issues Found:**
1. **MCP Package Issues**: Python MCP imports hanging/failing
2. **Environment Conflicts**: Possible dependency conflicts
3. **Server Startup**: Asyncio event loop issues

### **Diagnostic Commands:**
```bash
# Check Python environment
/Users/<USER>/.pyenv/versions/3.10.13/bin/python3 --version

# Check MCP installation
/Users/<USER>/.pyenv/versions/3.10.13/bin/python3 -c "import mcp; print('OK')"

# Check dependencies
/Users/<USER>/.pyenv/versions/3.10.13/bin/python3 -m pip list | grep mcp
```

## 🛠️ **Fix Options:**

### **Option 1: Clean Python Environment (RECOMMENDED)**
```bash
cd /Users/<USER>/Documents/augment-projects/option-buy-mcp

# Create fresh virtual environment
python3 -m venv venv_mcp
source venv_mcp/bin/activate

# Install clean dependencies
pip install --upgrade pip
pip install mcp>=1.0.0
pip install yfinance pandas numpy ta

# Test minimal server
python3 src/minimal_server.py
```

### **Option 2: Use Node.js MCP Server**
Create a Node.js version of the server:
```bash
npm init -y
npm install @modelcontextprotocol/sdk
# Implement server in JavaScript
```

### **Option 3: Use Filesystem + Custom Scripts**
Use the filesystem MCP to access Python scripts:
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/augment-projects/option-buy-mcp"]
    }
  }
}
```

Then ask Claude to:
- "Read and execute the analysis script in my project"
- "Run the Nifty options analysis using the Python files"

## 🎯 **Alternative Approach - Manual Analysis:**

Since the MCP server has issues, you can still use the analysis logic:

### **Run Analysis Manually:**
```bash
cd /Users/<USER>/Documents/augment-projects/option-buy-mcp
python3 test_mcp.py
```

### **Get Market Data:**
```bash
python3 -c "
import sys
sys.path.append('src')
from data_fetcher import DataFetcher
import asyncio

async def get_data():
    fetcher = DataFetcher()
    data = await fetcher.get_nifty_data()
    print(f'Nifty: {data[\"current_price\"]} ({data[\"change_percent\"]:+.2f}%)')
    await fetcher.close()

asyncio.run(get_data())
"
```

## 📋 **Working Configuration (Current):**

Your Claude Desktop is now configured with:
- ✅ **Filesystem MCP**: Access project files
- ✅ **Puppeteer MCP**: Web automation

### **How to Use:**
1. **"List all Python files in my option trading project"**
2. **"Read the data_fetcher.py file and explain how it works"**
3. **"Take a screenshot of NSE India website"**
4. **"Execute the test script and show me the results"**

## 🚀 **Next Steps:**

1. **Test Current Setup**: Verify filesystem and puppeteer MCPs work
2. **Fix Python Environment**: Try Option 1 above
3. **Alternative Implementation**: Consider Node.js version
4. **Manual Usage**: Use Python scripts directly

## 📞 **If You Need Help:**

1. **Test the current setup first** - filesystem MCP should work
2. **Try the manual Python scripts** - they contain all the logic
3. **Consider the Node.js approach** - often more reliable for MCP

The core analysis logic is solid - it's just the MCP server wrapper that needs fixing! 🚀
